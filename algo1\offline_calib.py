import cv2, numpy as np, glob

# ---------- 用户参数 ----------
PROJ_CROSS = r"D:\Dataset\keystone\20251103-171052.jpg"          # 十字校准图
PATTERN_CHESS = (4, 5)            # 角点网格
N_POSE = 3                       # 姿态数
# ------------------------------

objp = np.zeros((np.prod(PATTERN_CHESS), 3), np.float32)
objp[:, :2] = np.mgrid[0:PATTERN_CHESS[0], 0:PATTERN_CHESS[1]].T.reshape(-1, 2)  # 单位棋盘格

img_pts = []   # 相机像素
obj_pts = []   # 3-D 坐标（以格为单元）
proj_pts = []  # 投影仪像素（固定）

# 1. 投影仪角点 = 固定值（同一幅图）
cross_gray = cv2.imread(PROJ_CROSS, 0)
ret, corners_p = cv2.findChessboardCorners(cross_gray, PATTERN_CHESS)

ret = True
corners_p = np.array([[320, 134], [639, 134], [960, 134], [1279, 135], [1600, 134], [320, 405], [640, 404], [960, 406], [1280, 406], [1601, 405], [319, 674], [639, 676], [960, 675], [1280, 676], [1599, 675], [319, 944], [640, 944], [960, 944], [1281, 945], [1600, 944]], dtype=np.float32)

corners_p = np.reshape(corners_p, (-1, 1, 2))
#print("corners_p", corners_p)
cv2.cornerSubPix(cross_gray, corners_p, (11, 11), (-1, -1),
                 (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001))
proj_pts = corners_p.reshape(-1, 2)  # M×2

# 2. 遍历相机拍到的图
images = sorted(glob.glob('calib_*.jpg'))
images_dir = r"D:\Dataset\keystone"
import os
images = ["cam_1080p_5.jpg", "cam_1080p_6.jpg", "cam_1080p_7.jpg"]
assert len(images) >= N_POSE
corners_c_dict = {"cam_1080p_5.jpg": np.array([[276, 161], [429, 159], [586, 156], [745, 154], [907, 153], [258, 282], [420, 280], [583, 279], [751, 278], [920, 276], [237, 415], [407, 414], [580, 414], [756, 414], [934, 415], [216, 562], [393, 562], [576, 564], [761, 566], [950, 567]],dtype=np.float32),
                  "cam_1080p_6.jpg": np.array([[298, 162], [453, 161], [609, 158], [768, 156], [929, 153], [281, 282], [443, 282], [607, 280], [773, 279], [943, 279], [262, 416], [432, 415], [604, 415], [780, 415], [958, 414], [241, 564], [420, 565], [602, 565], [787, 567], [975, 567]],dtype=np.float32),
                  "cam_1080p_7.jpg": np.array([[242, 158], [396, 157], [552, 155], [712, 152], [875, 150], [221, 279], [383, 279], [548, 278], [715, 275], [886, 273], [198, 414], [369, 414], [541, 412], [719, 411], [900, 411], [174, 561], [354, 563], [536, 563], [722, 564], [911, 565]],dtype=np.float32)}
for fname in images[:N_POSE]:
    img = cv2.imread(os.path.join(images_dir, fname))
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    ret, corners_c = cv2.findChessboardCorners(gray, PATTERN_CHESS)
    ret = True
    corners_c = corners_c_dict[fname]
    corners_c = np.reshape(corners_c, (-1, 1, 2))
    if ret:
        cv2.cornerSubPix(gray, corners_c, (11, 11), (-1, -1),
                         (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001))
        img_pts.append(corners_c.reshape(-1, 2))
        obj_pts.append(objp)          # 同一组 3-D 点

#print("img_pts", img_pts)


# 3. 相机标定（拿 Kc）
#Kc = np.load('Kc.npy')  
Kc = np.array([ 8.3829501611528804e+02, 0., 6.1849577402276270e+02, 0.,
       8.5114568888309248e+02, 4.0359283026107522e+02, 0., 0., 1. ],dtype=np.float32) # 用户已知
Kc = np.reshape(Kc, (3, 3))
np.save('Kc.npy', Kc)
dist = np.zeros(5)       # 假设无畸变或已去畸变

# 4. 求每幅图的外参 → 得到 **相机坐标系下** 的 3-D 点
X_cam = []               # 累积所有 3-D 点
for i, (ipt, opt) in enumerate(zip(img_pts, obj_pts)):
    _, rvec, tvec = cv2.solvePnP(opt, ipt, Kc, dist)
    R, _ = cv2.Rodrigues(rvec)
    # 把棋盘格点转到相机坐标系
    X = (R @ opt.T + tvec).T
    X_cam.append(X)

X_cam = np.vstack(X_cam)        # N×3
proj_pts = np.vstack([proj_pts]*len(X_cam))  # 对应投影仪像素

# 5. DLT 求 G
def DLT(X, x):
    """ X: N×3, x: N×2 → 返回 3×4 G """
    A = []
    for i in range(X.shape[0]):
        Xi = np.append(X[i], 1)          # 4×1
        ui, vi = x[i]
        A.extend([ui*Xi, vi*Xi, Xi])       # 3×4 堆叠
    A = np.array(A)
    _, _, V = np.linalg.svd(A)
    print(V.shape)
    G = V[:-1, :].reshape(3, 4)
    return G

G = DLT(X_cam, proj_pts)
print('G (camera → projector):\n', G)
np.save('G.npy', G)