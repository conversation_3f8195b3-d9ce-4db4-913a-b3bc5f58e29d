import cv2, numpy as np, threading, time


# ---------- 参数 ----------
Kc = np.load('Kc.npy')

G = np.load('G.npy')
STAR_HUB = 'starhub.png'      # 原始内容
GREEN_BORDER = 8              # 绿框像素
# ----------------------------

# 1. 生成带绿框的校准图
def make_green_frame(img):
    h, w = img.shape[:2]
    canvas = cv2.copyMakeBorder(img, GREEN_BORDER, GREEN_BORDER,
                                GREEN_BORDER, GREEN_BORDER,
                                cv2.BORDER_CONSTANT, value=(0, 255, 0))
    return canvas, (w, h)       # 返回画布 + 原图尺寸

# 2. 检测四边形（简化版）
def detect_quad(gray):
    edges = cv2.Canny(gray, 50, 150)
    lines = cv2.HoughLinesP(edges, 1, np.pi/180, 80,
                             minLineLength=80, maxLineGap=10)
    if lines is None:
        return None
    # 暴力合并四边形（仅示例，生产请用 LSD + 聚类）
    pts = []
    for l in lines:
        x1, y1, x2, y2 = l[0]
        pts.extend([(x1, y1), (x2, y2)])
    pts = np.array(pts, dtype=np.float32)
    rect = cv2.minAreaRect(pts)
    box = cv2.boxPoints(rect)  # 4×2
    return np.int0(box)

# 3. 三角化 + 内接矩形
def triangulate_and_rect(xc, xp, Kc, G):
    """ xc, xp: 4×2 角点 """
    X = []
    for i in range(4):
        # 线性三角化
        print((xc[i][0] * Kc[2] - Kc[0]).shape, (xc[i][1] * Kc[2] - Kc[1]).shape, (xp[i][0] * G[2] - G[0]).shape, (xp[i][1] * G[2] - G[1]).shape)
        A = np.vstack([xc[i][0] * Kc[2, :] - Kc[0, :],
                       xc[i][1] * Kc[2, :] - Kc[1, :],
                       xp[i][0] * G[2, :] - G[0, :],
                       xp[i][1] * G[2, :] - G[1, :]])
        _, _, V = np.linalg.svd(A)
        Xh = V[-1]
        Xh /= Xh[3]
        X.append(Xh[:3])
    X = np.array(X)                    # 4×3 相机坐标
    # 简化：直接取 XY 框
    xy = X[:, :2]
    rect = cv2.minAreaRect(xy.astype(np.float32))
    box = cv2.boxPoints(rect)
    return box                         # 4×2

# 4. 计算预扭曲 homography
def compute_homography(src_pts, dst_pts):
    H, _ = cv2.findHomography(src_pts, dst_pts, cv2.RANSAC, 5.0)
    return H

# 5. 主循环
def main():
    canvas = cv2.imread(r"D:\Dataset\keystone\cam_1080p_5.jpg")
    quad = np.array([[134,103], [1062, 89], [1151,650], [28, 638]], dtype=np.float32)
    if quad is not None:
        # 对应投影仪角点 = 绿框四角
        h_canvas, w_canvas = canvas.shape[:2]
        xp = np.array([[0, 0], [w_canvas, 0], [w_canvas, h_canvas], [0, h_canvas]], dtype=np.float32)
        xc = quad.astype(np.float32)
        # 三角化→内接矩形
        rect_xy = triangulate_and_rect(xc, xp, Kc, G)
        # 映射回投影仪像素
        H = compute_homography(xp, rect_xy)
        warped = cv2.warpPerspective(canvas, H, (w_canvas, h_canvas))
    else:
        warped = canvas
    cv2.imshow('proj', warped)
    cv2.waitKey() 

    cv2.destroyAllWindows()


    # cap = cv2.VideoCapture(0)
    # img_orig = cv2.imread(STAR_HUB)
    # canvas, (w_orig, h_orig) = make_green_frame(img_orig)
    # cv2.namedWindow('proj', cv2.WINDOW_NORMAL)
    # cv2.setWindowProperty('proj', cv2.WND_PROP_FULLSCREEN, cv2.WINDOW_FULLSCREEN)

    # while True:
    #     ret, frame = cap.read()
    #     if not ret:
    #         break
    #     gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    #     quad = detect_quad(gray)
    #     if quad is not None:
    #         # 对应投影仪角点 = 绿框四角
    #         h_canvas, w_canvas = canvas.shape[:2]
    #         xp = np.array([[0, 0], [w_canvas, 0], [w_canvas, h_canvas], [0, h_canvas]], dtype=np.float32)
    #         xc = quad.astype(np.float32)
    #         # 三角化→内接矩形
    #         rect_xy = triangulate_and_rect(xc, xp, Kc, G)
    #         # 映射回投影仪像素
    #         H = compute_homography(xp, rect_xy)
    #         warped = cv2.warpPerspective(canvas, H, (w_canvas, h_canvas))
    #     else:
    #         warped = canvas
    #     cv2.imshow('proj', warped)
    #     if cv2.waitKey(1) & 0xFF == ord('q'):
    #         break
    # cap.release()
    # cv2.destroyAllWindows()

if __name__ == '__main__':
    main()